package com.xiaozhi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Third-party MCP configuration properties
 */
@Data
@Component
@ConfigurationProperties(prefix = "xiaozhi.mcp.thirdparty")
public class ThirdPartyMcpConfig {
    
    /**
     * Whether third-party MCP functionality is enabled
     */
    private boolean enabled = true;
}