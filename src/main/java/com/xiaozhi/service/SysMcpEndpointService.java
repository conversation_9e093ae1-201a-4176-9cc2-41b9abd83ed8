package com.xiaozhi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xiaozhi.entity.SysMcpEndpoint;

import java.util.List;

/**
 * MCP endpoint service
 */
public interface SysMcpEndpointService extends IService<SysMcpEndpoint> {
    
    /**
     * Get all enabled MCP endpoints
     * 
     * @return list of enabled endpoints
     */
    List<SysMcpEndpoint> getEnabledEndpoints();
    
    /**
     * Add a new MCP endpoint
     * 
     * @param endpoint the endpoint to add
     * @return true if successful, false otherwise
     */
    boolean addEndpoint(SysMcpEndpoint endpoint);
    
    /**
     * Update an existing MCP endpoint
     * 
     * @param endpoint the endpoint to update
     * @return true if successful, false otherwise
     */
    boolean updateEndpoint(SysMcpEndpoint endpoint);
    
    /**
     * Delete an MCP endpoint by ID
     * 
     * @param id the endpoint ID
     * @return true if successful, false otherwise
     */
    boolean deleteEndpoint(Long id);
    
    /**
     * Toggle endpoint enabled status
     * 
     * @param id the endpoint ID
     * @param enabled the new enabled status
     * @return true if successful, false otherwise
     */
    boolean toggleEndpointStatus(Long id, Boolean enabled);
}