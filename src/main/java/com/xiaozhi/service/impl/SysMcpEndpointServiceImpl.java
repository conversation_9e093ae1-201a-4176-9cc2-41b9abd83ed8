package com.xiaozhi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.dao.SysMcpEndpointMapper;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.service.SysMcpEndpointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * MCP endpoint service implementation
 */
@Service
public class SysMcpEndpointServiceImpl implements SysMcpEndpointService {

    @Autowired
    private SysMcpEndpointMapper mcpEndpointMapper;
    
    @Override
    public List<SysMcpEndpoint> getEnabledEndpoints() {
        QueryWrapper<SysMcpEndpoint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enabled", true);
        queryWrapper.eq("del_flag", 0);
        return mcpEndpointMapper.
    }
    
    @Override
    public boolean addEndpoint(SysMcpEndpoint endpoint) {
        return save(endpoint);
    }
    
    @Override
    public boolean updateEndpoint(SysMcpEndpoint endpoint) {
        return updateById(endpoint);
    }
    
    @Override
    public boolean deleteEndpoint(Integer id) {
        return removeById(id);
    }
    
    @Override
    public boolean toggleEndpointStatus(Integer id, Boolean enabled) {
        SysMcpEndpoint endpoint = new SysMcpEndpoint();
        endpoint.setId(id);
        endpoint.setIsEnabled(enabled);
        return updateById(endpoint);
    }
}