package com.xiaozhi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.config.McpConfig;
import com.xiaozhi.dao.SysMcpEndpointMapper;
import com.xiaozhi.dto.McpEndpointDto;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.service.SysMcpEndpointService;
import com.xiaozhi.utils.JsonUtil;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.transport.HttpClientSseClientTransport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.net.http.HttpRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP endpoint service implementation
 */
@Service
public class SysMcpEndpointServiceImpl extends ServiceImpl<SysMcpEndpointMapper, SysMcpEndpoint> implements SysMcpEndpointService {

    private static final Logger logger = LoggerFactory.getLogger(SysMcpEndpointServiceImpl.class);

    @Autowired
    private McpConfig mcpConfig;

    @Override
    public List<SysMcpEndpoint> getEnabledEndpoints() {
        QueryWrapper<SysMcpEndpoint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_enabled", true);
        queryWrapper.eq("is_deleted", false);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public Resp findPage(int pageNum, int pageSize, String name, Boolean enabled) {
        Page<SysMcpEndpoint> page = new Page<>(pageNum, pageSize);
        QueryWrapper<SysMcpEndpoint> queryWrapper = new QueryWrapper<>();

        // 查询条件
        queryWrapper.eq("is_deleted", false);
        if (StringUtils.hasText(name)) {
            queryWrapper.like("name", name);
        }
        if (enabled != null) {
            queryWrapper.eq("is_enabled", enabled);
        }
        queryWrapper.orderByDesc("created_at");

        IPage<SysMcpEndpoint> result = page(page, queryWrapper);

        Map<String, Object> data = new HashMap<>();
        data.put("records", result.getRecords());
        data.put("total", result.getTotal());
        data.put("current", result.getCurrent());
        data.put("size", result.getSize());
        data.put("pages", result.getPages());

        return Resp.succeed(data);
    }

    @Override
    public SysMcpEndpoint getById(Integer id) {
        QueryWrapper<SysMcpEndpoint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        queryWrapper.eq("is_deleted", false);
        return getOne(queryWrapper);
    }

    @Override
    public Resp addEndpoint(McpEndpointDto dto) {
        try {
            // 检查URL是否已存在
            QueryWrapper<SysMcpEndpoint> checkWrapper = new QueryWrapper<>();
            checkWrapper.eq("url", dto.getUrl());
            checkWrapper.eq("is_deleted", false);
            if (count(checkWrapper) > 0) {
                return Resp.fail(400, "该URL已存在");
            }

            SysMcpEndpoint endpoint = new SysMcpEndpoint();
            BeanUtils.copyProperties(dto, endpoint);
            endpoint.setIsEnabled(dto.getEnabled());
            endpoint.setCreatedAt(new Date());
            endpoint.setUpdatedAt(new Date());
            endpoint.setIsDeleted(false);

            // 处理headers
            if (dto.getHeaders() != null && !dto.getHeaders().isEmpty()) {
                // 将headers存储为JSON字符串到数据库的headers字段
                // 注意：这里需要在实体类中添加headers字段
            }

            boolean success = save(endpoint);
            if (success) {
                return Resp.succeed(endpoint);
            } else {
                return Resp.fail(500, "添加失败");
            }
        } catch (Exception e) {
            logger.error("添加MCP端点失败", e);
            return Resp.fail(500, "添加失败: " + e.getMessage());
        }
    }

    @Override
    public Resp updateEndpoint(Integer id, McpEndpointDto dto) {
        try {
            SysMcpEndpoint existing = getById(id);
            if (existing == null) {
                return Resp.fail(404, "端点不存在");
            }

            // 检查URL是否与其他记录冲突
            if (!existing.getUrl().equals(dto.getUrl())) {
                QueryWrapper<SysMcpEndpoint> checkWrapper = new QueryWrapper<>();
                checkWrapper.eq("url", dto.getUrl());
                checkWrapper.eq("is_deleted", false);
                checkWrapper.ne("id", id);
                if (count(checkWrapper) > 0) {
                    return Resp.fail(400, "该URL已存在");
                }
            }

            BeanUtils.copyProperties(dto, existing, "id", "createdAt");
            existing.setIsEnabled(dto.getEnabled());
            existing.setUpdatedAt(new Date());

            boolean success = updateById(existing);
            if (success) {
                return Resp.succeed(existing);
            } else {
                return Resp.fail(500, "更新失败");
            }
        } catch (Exception e) {
            logger.error("更新MCP端点失败", e);
            return Resp.fail(500, "更新失败: " + e.getMessage());
        }
    }

    @Override
    public Resp deleteEndpoint(Integer id) {
        try {
            SysMcpEndpoint existing = getById(id);
            if (existing == null) {
                return Resp.fail(404, "端点不存在");
            }

            // 软删除
            existing.setIsDeleted(true);
            existing.setUpdatedAt(new Date());

            boolean success = updateById(existing);
            if (success) {
                return Resp.succeed("删除成功");
            } else {
                return Resp.fail(500, "删除失败");
            }
        } catch (Exception e) {
            logger.error("删除MCP端点失败", e);
            return Resp.fail(500, "删除失败: " + e.getMessage());
        }
    }

    @Override
    public Resp toggleEndpointStatus(Integer id, Boolean enabled) {
        try {
            SysMcpEndpoint existing = getById(id);
            if (existing == null) {
                return Resp.fail(404, "端点不存在");
            }

            existing.setIsEnabled(enabled);
            existing.setUpdatedAt(new Date());

            boolean success = updateById(existing);
            if (success) {
                return Resp.succeed("状态更新成功");
            } else {
                return Resp.fail(500, "状态更新失败");
            }
        } catch (Exception e) {
            logger.error("更新MCP端点状态失败", e);
            return Resp.fail(500, "状态更新失败: " + e.getMessage());
        }
    }

    @Override
    public Resp testEndpointConnection(String url, String authToken) {
        try {
            if (!StringUtils.hasText(url)) {
                return Resp.fail(400, "URL不能为空");
            }

            var transport = HttpClientSseClientTransport
                    .builder(url)
                    .sseEndpoint("/mcp_server/sse")
                    .requestBuilder(HttpRequest.newBuilder().header(HttpHeaders.AUTHORIZATION, STR."Bearer \{authToken}"))
                    .build();

            try (var client = McpClient.sync(transport).build()) {
                var initializeResult = client.initialize();

                if (initializeResult == null) {
                    return Resp.fail(500, "无法连接到MCP端点");
                }

                client.ping();
                var listToolsResult = client.listTools();

                Map<String, Object> result = new HashMap<>();
                result.put("connected", true);
                result.put("toolsCount", listToolsResult.tools().size());
                result.put("serverInfo", initializeResult.serverInfo());

                return Resp.succeed(result);
            }
        } catch (Exception e) {
            logger.error("测试MCP端点连接失败: {}", url, e);
            return Resp.fail(500, "连接测试失败: " + e.getMessage());
        }
    }
}