package com.xiaozhi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaozhi.dao.SysMcpEndpointMapper;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.service.SysMcpEndpointService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * MCP endpoint service implementation
 */
@Service
public class SysMcpEndpointServiceImpl extends ServiceImpl<SysMcpEndpointMapper, SysMcpEndpoint> implements SysMcpEndpointService {
    
    @Override
    public List<SysMcpEndpoint> getEnabledEndpoints() {
        QueryWrapper<SysMcpEndpoint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enabled", true);
        queryWrapper.eq("del_flag", 0);
        return list(queryWrapper);
    }
    
    @Override
    public boolean addEndpoint(SysMcpEndpoint endpoint) {
        return save(endpoint);
    }
    
    @Override
    public boolean updateEndpoint(SysMcpEndpoint endpoint) {
        return updateById(endpoint);
    }
    
    @Override
    public boolean deleteEndpoint(Long id) {
        return removeById(id);
    }
    
    @Override
    public boolean toggleEndpointStatus(Long id, Boolean enabled) {
        SysMcpEndpoint endpoint = new SysMcpEndpoint();
        endpoint.setId(id);
        endpoint.setEnabled(enabled);
        return updateById(endpoint);
    }
}