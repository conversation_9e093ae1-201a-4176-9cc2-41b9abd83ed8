package com.xiaozhi.controller;

import com.xiaozhi.common.web.AjaxResult;
import com.xiaozhi.dialogue.llm.tool.mcp.thirdparty.ThirdPartyMcpService;
import com.xiaozhi.utils.JsonUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * MCP Endpoint Controller for retrieving available tools from MCP URLs
 */
@Tag(name = "McpEndpoint", description = "MCP端点工具查询")
@RestController
@RequestMapping("/api/v1/mcp/endpoint")
public class McpEndpointController {
    
    private static final Logger logger = LoggerFactory.getLogger(McpEndpointController.class);
    
    @Resource
    private ThirdPartyMcpService thirdPartyMcpService;
    
    @Operation(summary = "获取MCP端点可用工具", description = "传入MCP URL，返回该端点的可用工具列表")
    @PostMapping("/tools")
    public AjaxResult getAvailableTools(@RequestParam String mcpUrl,
                                       @RequestBody(required = false) Map<String, String> headers) {
        try {
            // Validate URL
            if (mcpUrl == null || mcpUrl.trim().isEmpty()) {
                return AjaxResult.error("MCP URL不能为空");
            }
            
            // Create WebClient for the endpoint
            WebClient webClient = createWebClient(mcpUrl, headers);
            
            // Initialize MCP connection
            Map<String, Object> initializeResult = initializeConnection(webClient);
            if (initializeResult == null) {
                return AjaxResult.error("无法连接到MCP端点，请检查URL和网络连接");
            }
            
            // Get tools list
            List<Map<String, Object>> tools = getToolsList(webClient);
            if (tools == null) {
                return AjaxResult.error("获取工具列表失败");
            }
            
            // Format response data
            Map<String, Object> responseData = Map.of(
                "endpoint", mcpUrl,
                "toolsCount", tools.size(),
                "tools", tools
            );
            
            return AjaxResult.success("成功获取MCP端点工具列表", responseData);
            
        } catch (WebClientResponseException e) {
            logger.error("HTTP error when connecting to MCP endpoint {}: {}", mcpUrl, e.getMessage());
            return AjaxResult.error("连接MCP端点时发生HTTP错误: " + e.getStatusCode() + " - " + e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.error("Error getting tools from MCP endpoint {}", mcpUrl, e);
            return AjaxResult.error("获取MCP端点工具时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * Create a WebClient for the given endpoint
     */
    private WebClient createWebClient(String endpointUrl, Map<String, String> headers) {
        WebClient.Builder builder = WebClient.builder()
                .baseUrl(endpointUrl)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        
        // Add any custom headers
        if (headers != null) {
            headers.forEach(builder::defaultHeader);
        }
        
        return builder.build();
    }
    
    /**
     * Initialize MCP connection
     */
    private Map<String, Object> initializeConnection(WebClient webClient) {
        try {
            Map<String, Object> initRequest = Map.of(
                "jsonrpc", "2.0",
                "id", System.currentTimeMillis(),
                "method", "initialize",
                "params", Map.of(
                    "protocolVersion", "2024-11-05",
                    "capabilities", Map.of(),
                    "clientInfo", Map.of(
                        "name", "xiaozhi-mcp-client",
                        "version", "1.0.0"
                    )
                )
            );
            
            String responseJson = webClient.post()
                    .bodyValue(initRequest)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofMillis(10000)) // 10 second timeout
                    .block();
            
            return JsonUtil.fromJson(responseJson, new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            logger.error("Error initializing MCP connection", e);
            return null;
        }
    }
    
    /**
     * Get tools list from MCP endpoint
     */
    private List<Map<String, Object>> getToolsList(WebClient webClient) {
        try {
            Map<String, Object> toolsRequest = Map.of(
                "jsonrpc", "2.0",
                "id", System.currentTimeMillis(),
                "method", "tools/list",
                "params", Map.of()
            );
            
            String responseJson = webClient.post()
                    .bodyValue(toolsRequest)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofMillis(10000)) // 10 second timeout
                    .block();
            
            Map<String, Object> response = JsonUtil.fromJson(responseJson, new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});
            
            if (response.containsKey("result")) {
                Map<String, Object> result = (Map<String, Object>) response.get("result");
                return (List<Map<String, Object>>) result.get("tools");
            } else if (response.containsKey("error")) {
                logger.error("MCP endpoint returned error: {}", response.get("error"));
                return null;
            }
            
            return List.of(); // Return empty list if no tools
        } catch (Exception e) {
            logger.error("Error getting tools list from MCP endpoint", e);
            return null;
        }
    }
}
