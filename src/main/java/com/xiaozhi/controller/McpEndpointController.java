package com.xiaozhi.controller;

import com.xiaozhi.common.interceptor.PassAuth;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.config.McpConfig;

import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.transport.HttpClientSseClientTransport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.HttpHeaders;

import java.net.http.HttpRequest;

/**
 * MCP Endpoint Controller for retrieving available tools from MCP URLs
 */
@Tag(name = "McpEndpoint", description = "MCP端点工具查询")
@RestController
@RequestMapping("/mcp-servers")
public class McpEndpointController {

    private static final Logger logger = LoggerFactory.getLogger(McpEndpointController.class);

    @Resource
    private McpConfig mcpConfig;

    @PassAuth
    @Operation(summary = "获取MCP端点可用工具", description = "传入MCP URL，返回该端点的可用工具列表")
    @PostMapping("/tools")
    public Resp getAvailableTools(@RequestParam String mcpUrl, @RequestParam String mcpToken) {
        // Validate URL
        if (mcpUrl == null || mcpUrl.trim().isEmpty()) {
            return Resp.fail(400, "MCP URL不能为空");
        }

        var transport = HttpClientSseClientTransport
                .builder(mcpUrl)
                .sseEndpoint("/mcp_server/sse")
                .requestBuilder(HttpRequest.newBuilder().header(HttpHeaders.AUTHORIZATION, STR."Bearer \{mcpToken}"))
                .build();
        try (var client = McpClient.sync(transport).build()) {
            var initializeResult = client.initialize();

            if (initializeResult == null) {
                return Resp.fail(500, "无法连接到MCP端点，请检查URL和网络连接");
            }

            client.ping();
            var listToolsResult = client.listTools();

            return Resp.succeed(listToolsResult.tools());
        }

    }
}
