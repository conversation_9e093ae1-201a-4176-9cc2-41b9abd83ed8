package com.xiaozhi.controller;

import com.xiaozhi.common.interceptor.PassAuth;
import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.config.McpConfig;
import com.xiaozhi.dto.McpEndpointDto;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.service.SysMcpEndpointService;

import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.transport.HttpClientSseClientTransport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.HttpHeaders;

import java.net.http.HttpRequest;

/**
 * MCP Endpoint Controller for managing MCP endpoints and retrieving available tools
 */
@Tag(name = "McpEndpoint", description = "MCP端点管理")
@RestController
@RequestMapping("/api/v1/mcp-endpoints")
public class McpEndpointController {

    private static final Logger logger = LoggerFactory.getLogger(McpEndpointController.class);

    @Resource
    private McpConfig mcpConfig;

    @Resource
    private SysMcpEndpointService mcpEndpointService;

    // ==================== CRUD Operations ====================

    @Operation(summary = "获取MCP端点列表", description = "分页查询MCP端点列表")
    @GetMapping
    public Resp list(@RequestParam(defaultValue = "1") int pageNum,
                     @RequestParam(defaultValue = "10") int pageSize,
                     @RequestParam(required = false) String name,
                     @RequestParam(required = false) Boolean enabled) {
        return mcpEndpointService.findPage(pageNum, pageSize, name, enabled);
    }

    @Operation(summary = "获取MCP端点详情", description = "根据ID获取MCP端点详情")
    @GetMapping("/{id}")
    public Resp getById(@PathVariable Integer id) {
        SysMcpEndpoint endpoint = mcpEndpointService.getById(id);
        if (endpoint == null) {
            return Resp.fail(404, "端点不存在");
        }
        return Resp.succeed(endpoint);
    }

    @Operation(summary = "添加MCP端点", description = "添加新的MCP端点")
    @PostMapping
    public Resp add(@RequestBody McpEndpointDto dto) {
        return mcpEndpointService.addEndpoint(dto);
    }

    @Operation(summary = "更新MCP端点", description = "更新指定的MCP端点")
    @PutMapping("/{id}")
    public Resp update(@PathVariable Integer id, @RequestBody McpEndpointDto dto) {
        return mcpEndpointService.updateEndpoint(id, dto);
    }

    @Operation(summary = "删除MCP端点", description = "删除指定的MCP端点")
    @DeleteMapping("/{id}")
    public Resp delete(@PathVariable Integer id) {
        return mcpEndpointService.deleteEndpoint(id);
    }

    @Operation(summary = "切换端点状态", description = "启用或禁用MCP端点")
    @PutMapping("/{id}/status")
    public Resp toggleStatus(@PathVariable Integer id, @RequestParam Boolean enabled) {
        return mcpEndpointService.toggleEndpointStatus(id, enabled);
    }

    // ==================== Tool Operations ====================

    @PassAuth
    @Operation(summary = "获取MCP端点可用工具", description = "传入MCP URL，返回该端点的可用工具列表")
    @PostMapping("/tools")
    public Resp getAvailableTools(@RequestParam String mcpUrl, @RequestParam String mcpToken) {
        // Validate URL
        if (mcpUrl == null || mcpUrl.trim().isEmpty()) {
            return Resp.fail(400, "MCP URL不能为空");
        }

        var transport = HttpClientSseClientTransport
                .builder(mcpUrl)
                .sseEndpoint("/mcp_server/sse")
                .requestBuilder(HttpRequest.newBuilder().header(HttpHeaders.AUTHORIZATION, "Bearer " + mcpToken))
                .build();
        try (var client = McpClient.sync(transport).build()) {
            var initializeResult = client.initialize();

            if (initializeResult == null) {
                return Resp.fail(500, "无法连接到MCP端点，请检查URL和网络连接");
            }

            client.ping();
            var listToolsResult = client.listTools();

            return Resp.succeed(listToolsResult.tools());
        } catch (Exception e) {
            logger.error("获取MCP端点工具失败: {}", mcpUrl, e);
            return Resp.fail(500, "获取工具失败: " + e.getMessage());
        }
    }

    @Operation(summary = "测试MCP端点连接", description = "测试指定MCP端点的连接状态")
    @PostMapping("/test-connection")
    public Resp testConnection(@RequestParam String url, @RequestParam(required = false) String authToken) {
        return mcpEndpointService.testEndpointConnection(url, authToken);
    }

    @Operation(summary = "获取已保存端点的工具", description = "获取已保存的MCP端点的可用工具")
    @GetMapping("/{id}/tools")
    public Resp getEndpointTools(@PathVariable Integer id) {
        SysMcpEndpoint endpoint = mcpEndpointService.getById(id);
        if (endpoint == null) {
            return Resp.fail(404, "端点不存在");
        }

        if (!endpoint.getIsEnabled()) {
            return Resp.fail(400, "端点已禁用");
        }

        return getAvailableTools(endpoint.getUrl(), endpoint.getAuthToken());
    }
}
