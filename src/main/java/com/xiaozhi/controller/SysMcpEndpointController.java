package com.xiaozhi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaozhi.common.web.AjaxResult;
import com.xiaozhi.common.web.PageFilter;
import com.xiaozhi.dto.McpEndpointDto;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.service.SysMcpEndpointService;
import com.xiaozhi.utils.JsonUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * MCP endpoint controller
 */
@Tag(name = "McpEndpoint", description = "MCP端点管理")
@RestController
@RequestMapping("/api/v1/mcp/endpoints")
public class SysMcpEndpointController {
    
    @Resource
    private SysMcpEndpointService sysMcpEndpointService;
    
    @Operation(summary = "获取MCP端点列表", description = "分页获取MCP端点列表")
    @GetMapping("/list")
    public AjaxResult list(PageFilter pageFilter, SysMcpEndpoint endpoint) {
        Page<SysMcpEndpoint> page = new Page<>(pageFilter.getStart(), pageFilter.getLimit());
        QueryWrapper<SysMcpEndpoint> queryWrapper = new QueryWrapper<>();
        
        // Add query conditions if provided
        if (endpoint.getUrl() != null && !endpoint.getUrl().isEmpty()) {
            queryWrapper.like("url", endpoint.getUrl());
        }
        if (endpoint.getName() != null && !endpoint.getName().isEmpty()) {
            queryWrapper.like("name", endpoint.getName());
        }
        if (endpoint.getEnabled() != null) {
            queryWrapper.eq("enabled", endpoint.getEnabled());
        }
        
        queryWrapper.eq("del_flag", 0);
        queryWrapper.orderByDesc("create_time");
        
        Page<SysMcpEndpoint> result = sysMcpEndpointService.page(page, queryWrapper);
        return AjaxResult.success("查询成功", result);
    }
    
    @Operation(summary = "获取所有启用的MCP端点", description = "获取所有启用状态的MCP端点")
    @GetMapping("/enabled")
    public AjaxResult getEnabledEndpoints() {
        List<SysMcpEndpoint> endpoints = sysMcpEndpointService.getEnabledEndpoints();
        return AjaxResult.success("查询成功", endpoints);
    }
    
    @Operation(summary = "获取MCP端点详情", description = "根据ID获取MCP端点详情")
    @GetMapping("/{id}")
    public AjaxResult getEndpoint(@PathVariable Long id) {
        SysMcpEndpoint endpoint = sysMcpEndpointService.getById(id);
        if (endpoint == null || endpoint.getDelFlag() == 1) {
            return AjaxResult.error("端点不存在或已被删除");
        }
        
        // Convert to DTO
        McpEndpointDto dto = new McpEndpointDto();
        BeanUtils.copyProperties(endpoint, dto);
        
        // Parse headers if present
        if (endpoint.getHeaders() != null && !endpoint.getHeaders().isEmpty()) {
            try {
                dto.setHeaders(JsonUtil.fromJson(
                    endpoint.getHeaders(), 
                    new com.fasterxml.jackson.core.type.TypeReference<java.util.Map<String, String>>() {}
                ));
            } catch (Exception e) {
                // Ignore parsing errors
            }
        }
        
        return AjaxResult.success("查询成功", dto);
    }
    
    @Operation(summary = "添加MCP端点", description = "添加新的MCP端点")
    @PostMapping("/add")
    public AjaxResult addEndpoint(@RequestBody McpEndpointDto dto) {
        // Validate required fields
        if (dto.getUrl() == null || dto.getUrl().isEmpty()) {
            return AjaxResult.error("端点URL不能为空");
        }
        if (dto.getName() == null || dto.getName().isEmpty()) {
            return AjaxResult.error("端点名称不能为空");
        }
        
        // Check if URL already exists
        QueryWrapper<SysMcpEndpoint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("url", dto.getUrl());
        queryWrapper.eq("del_flag", 0);
        if (sysMcpEndpointService.count(queryWrapper) > 0) {
            return AjaxResult.error("该端点URL已存在");
        }
        
        // Convert DTO to entity
        SysMcpEndpoint endpoint = new SysMcpEndpoint();
        BeanUtils.copyProperties(dto, endpoint);
        
        // Serialize headers if present
        if (dto.getHeaders() != null && !dto.getHeaders().isEmpty()) {
            try {
                endpoint.setHeaders(JsonUtil.toJson(dto.getHeaders()));
            } catch (Exception e) {
                return AjaxResult.error("头部信息格式错误: " + e.getMessage());
            }
        }
        
        boolean success = sysMcpEndpointService.addEndpoint(endpoint);
        if (success) {
            return AjaxResult.success("添加成功");
        } else {
            return AjaxResult.error("添加失败");
        }
    }
    
    @Operation(summary = "更新MCP端点", description = "更新MCP端点信息")
    @PutMapping("/update")
    public AjaxResult updateEndpoint(@RequestBody McpEndpointDto dto) {
        if (dto.getId() == null) {
            return AjaxResult.error("端点ID不能为空");
        }
        
        // Validate required fields
        if (dto.getUrl() == null || dto.getUrl().isEmpty()) {
            return AjaxResult.error("端点URL不能为空");
        }
        if (dto.getName() == null || dto.getName().isEmpty()) {
            return AjaxResult.error("端点名称不能为空");
        }
        
        // Check if URL already exists for other endpoints
        QueryWrapper<SysMcpEndpoint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("url", dto.getUrl());
        queryWrapper.ne("id", dto.getId());
        queryWrapper.eq("del_flag", 0);
        if (sysMcpEndpointService.count(queryWrapper) > 0) {
            return AjaxResult.error("该端点URL已存在");
        }
        
        // Convert DTO to entity
        SysMcpEndpoint endpoint = new SysMcpEndpoint();
        BeanUtils.copyProperties(dto, endpoint);
        
        // Serialize headers if present
        if (dto.getHeaders() != null && !dto.getHeaders().isEmpty()) {
            try {
                endpoint.setHeaders(JsonUtil.toJson(dto.getHeaders()));
            } catch (Exception e) {
                return AjaxResult.error("头部信息格式错误: " + e.getMessage());
            }
        }
        
        boolean success = sysMcpEndpointService.updateEndpoint(endpoint);
        if (success) {
            return AjaxResult.success("更新成功");
        } else {
            return AjaxResult.error("更新失败");
        }
    }
    
    @Operation(summary = "删除MCP端点", description = "根据ID删除MCP端点")
    @DeleteMapping("/delete/{id}")
    public AjaxResult deleteEndpoint(@PathVariable Long id) {
        boolean success = sysMcpEndpointService.deleteEndpoint(id);
        if (success) {
            return AjaxResult.success("删除成功");
        } else {
            return AjaxResult.error("删除失败");
        }
    }
    
    @Operation(summary = "启用/禁用MCP端点", description = "切换MCP端点的启用状态")
    @PutMapping("/toggle/{id}")
    public AjaxResult toggleEndpointStatus(@PathVariable Long id, @RequestParam Boolean enabled) {
        boolean success = sysMcpEndpointService.toggleEndpointStatus(id, enabled);
        if (success) {
            return AjaxResult.success(enabled ? "端点已启用" : "端点已禁用");
        } else {
            return AjaxResult.error("操作失败");
        }
    }
}