package com.xiaozhi.dialogue.llm.tool.mcp.thirdparty;

import com.xiaozhi.common.web.AjaxResult;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.service.SysMcpEndpointService;
import com.xiaozhi.utils.JsonUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Controller for managing third-party MCP endpoints
 */
@Tag(name = "ThirdPartyMcp", description = "Third-party MCP endpoint management")
@RestController
@RequestMapping("/mcp/thirdparty")
public class ThirdPartyMcpController {
    
    @Resource
    private ThirdPartyMcpManager thirdPartyMcpManager;
    
    @Resource
    private SysMcpEndpointService sysMcpEndpointService;
    
    @Operation(summary = "Connect to a third-party MCP endpoint", description = "Connects to a third-party MCP endpoint and registers its tools")
    @PostMapping("/connect")
    public AjaxResult connectToEndpoint(@RequestParam String endpointUrl,
                                       @RequestBody(required = false) Map<String, String> headers) {
        try {
            boolean success = thirdPartyMcpManager.connectToEndpoint(endpointUrl, headers);
            if (success) {
                return AjaxResult.success("Successfully connected to MCP endpoint and registered tools");
            } else {
                return AjaxResult.error("Failed to connect to MCP endpoint");
            }
        } catch (Exception e) {
            return AjaxResult.error("Failed to connect to MCP endpoint: " + e.getMessage());
        }
    }
    
    @Operation(summary = "Connect to a stored MCP endpoint by ID", description = "Connects to a stored MCP endpoint by its database ID")
    @PostMapping("/connect/{id}")
    public AjaxResult connectToStoredEndpoint(@PathVariable Long id) {
        try {
            SysMcpEndpoint endpoint = sysMcpEndpointService.getById(id);
            if (endpoint == null || endpoint.getDelFlag() == 1) {
                return AjaxResult.error("端点不存在或已被删除");
            }
            
            if (!endpoint.getEnabled()) {
                return AjaxResult.error("端点已被禁用");
            }
            
            Map<String, String> headers = new HashMap<>();
            
            // Add auth token if present
            if (endpoint.getAuthToken() != null && !endpoint.getAuthToken().isEmpty()) {
                headers.put("Authorization", "Bearer " + endpoint.getAuthToken());
            }
            
            // Add other headers if present
            if (endpoint.getHeaders() != null && !endpoint.getHeaders().isEmpty()) {
                try {
                    Map<String, String> additionalHeaders = JsonUtil.fromJson(
                        endpoint.getHeaders(), 
                        new com.fasterxml.jackson.core.type.TypeReference<Map<String, String>>() {}
                    );
                    headers.putAll(additionalHeaders);
                } catch (Exception e) {
                    return AjaxResult.error("端点头部信息格式错误: " + e.getMessage());
                }
            }
            
            boolean success = thirdPartyMcpManager.connectToEndpoint(endpoint.getUrl(), headers);
            if (success) {
                return AjaxResult.success("成功连接到MCP端点并注册工具");
            } else {
                return AjaxResult.error("连接MCP端点失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("连接MCP端点时出错: " + e.getMessage());
        }
    }
    
    @Operation(summary = "Get all connected endpoints", description = "Gets all currently connected MCP endpoints")
    @GetMapping("/endpoints")
    public AjaxResult getConnectedEndpoints() {
        try {
            Set<String> endpoints = thirdPartyMcpManager.getConnectedEndpoints();
            return AjaxResult.success("Successfully retrieved connected endpoints", endpoints);
        } catch (Exception e) {
            return AjaxResult.error("Failed to retrieve connected endpoints: " + e.getMessage());
        }
    }
    
    @Operation(summary = "Disconnect from an MCP endpoint", description = "Disconnects from an MCP endpoint")
    @DeleteMapping("/disconnect")
    public AjaxResult disconnectFromEndpoint(@RequestParam String endpointUrl) {
        try {
            boolean success = thirdPartyMcpManager.disconnectFromEndpoint(endpointUrl);
            if (success) {
                return AjaxResult.success("Successfully disconnected from MCP endpoint");
            } else {
                return AjaxResult.error("Not connected to the specified MCP endpoint");
            }
        } catch (Exception e) {
            return AjaxResult.error("Failed to disconnect from MCP endpoint: " + e.getMessage());
        }
    }
    
    @Operation(summary = "Check connection status", description = "Checks if connected to a specific MCP endpoint")
    @GetMapping("/connected")
    public AjaxResult isConnectedToEndpoint(@RequestParam String endpointUrl) {
        try {
            boolean connected = thirdPartyMcpManager.isConnectedToEndpoint(endpointUrl);
            return AjaxResult.success(connected ? "Connected to MCP endpoint" : "Not connected to MCP endpoint",
                    connected);
        } catch (Exception e) {
            return AjaxResult.error("Failed to check connection status: " + e.getMessage());
        }
    }
}