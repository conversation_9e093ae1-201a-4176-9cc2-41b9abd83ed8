package com.xiaozhi.dialogue.llm.tool.mcp.thirdparty;

import com.xiaozhi.config.McpConfig;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manager for handling multiple third-party MCP connections
 */
@Component
public class ThirdPartyMcpManager {
    private static final Logger logger = LoggerFactory.getLogger(ThirdPartyMcpManager.class);
    
    @Resource
    private ThirdPartyMcpService thirdPartyMcpService;
    
    @Resource
    private McpConfig mcpConfig;
    
    // Map to store registered tools by endpoint URL
    private final Map<String, List<FunctionToolCallback>> registeredTools = new ConcurrentHashMap<>();
    
    /**
     * Connect to a third-party MCP endpoint and register its tools
     *
     * @param endpointUrl The URL of the third-party MCP endpoint
     * @param headers Optional headers to include in requests
     * @return true if connection and registration were successful, false otherwise
     */
    public boolean connectToEndpoint(String endpointUrl, Map<String, String> headers) {
        try {
            List<FunctionToolCallback> callbacks = thirdPartyMcpService.connectAndRegisterTools(endpointUrl, headers);
            if (!callbacks.isEmpty()) {
                registeredTools.put(endpointUrl, callbacks);
                logger.info("Successfully connected to MCP endpoint: {} with {} tools", endpointUrl, callbacks.size());
                return true;
            } else {
                logger.warn("No tools registered from MCP endpoint: {}", endpointUrl);
                return false;
            }
        } catch (Exception e) {
            logger.error("Failed to connect to MCP endpoint: {}", endpointUrl, e);
            return false;
        }
    }
    
    /**
     * Get all registered tools from all connected endpoints
     *
     * @return A list of all registered tools
     */
    public List<FunctionToolCallback> getAllRegisteredTools() {
        return registeredTools.values().stream()
                .flatMap(List::stream)
                .toList();
    }
    
    /**
     * Get tools registered from a specific endpoint
     *
     * @param endpointUrl The URL of the MCP endpoint
     * @return List of tools from that endpoint, or empty list if not connected
     */
    public List<FunctionToolCallback> getToolsFromEndpoint(String endpointUrl) {
        return registeredTools.getOrDefault(endpointUrl, List.of());
    }
    
    /**
     * Disconnect from an endpoint and remove its registered tools
     *
     * @param endpointUrl The URL of the MCP endpoint to disconnect from
     * @return true if successfully disconnected, false if not connected
     */
    public boolean disconnectFromEndpoint(String endpointUrl) {
        List<FunctionToolCallback> removed = registeredTools.remove(endpointUrl);
        boolean disconnected = thirdPartyMcpService.disconnectFromEndpoint(endpointUrl);
        if (removed != null || disconnected) {
            logger.info("Disconnected from MCP endpoint: {}", endpointUrl);
            return true;
        } else {
            logger.warn("Attempted to disconnect from non-registered MCP endpoint: {}", endpointUrl);
            return false;
        }
    }
    
    /**
     * Check if connected to a specific endpoint
     *
     * @param endpointUrl The URL of the MCP endpoint
     * @return true if connected, false otherwise
     */
    public boolean isConnectedToEndpoint(String endpointUrl) {
        return thirdPartyMcpService.isConnectedToEndpoint(endpointUrl);
    }
    
    /**
     * Get all connected endpoints
     *
     * @return Set of connected endpoint URLs
     */
    public Set<String> getConnectedEndpoints() {
        return thirdPartyMcpService.getConnectedEndpoints();
    }
}