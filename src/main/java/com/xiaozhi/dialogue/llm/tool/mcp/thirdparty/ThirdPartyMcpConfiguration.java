package com.xiaozhi.dialogue.llm.tool.mcp.thirdparty;

import com.xiaozhi.config.ThirdPartyMcpConfig;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Configuration class for third-party MCP services
 */
@Configuration
@ComponentScan(basePackageClasses = ThirdPartyMcpService.class)
@Import(ThirdPartyMcpConfig.class)
public class ThirdPartyMcpConfiguration {
}