package com.xiaozhi.dialogue.llm.tool.mcp.thirdparty;

import com.xiaozhi.config.ThirdPartyMcpConfig;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.service.SysMcpEndpointService;
import com.xiaozhi.utils.JsonUtil;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service that automatically connects to predefined third-party MCP endpoints at startup
 */
@Service
public class ThirdPartyMcpStartupService {
    private static final Logger logger = LoggerFactory.getLogger(ThirdPartyMcpStartupService.class);
    
    @Resource
    private ThirdPartyMcpManager thirdPartyMcpManager;
    
    @Resource
    private ThirdPartyMcpConfig thirdPartyMcpConfig;
    
    @Resource
    private SysMcpEndpointService sysMcpEndpointService;
    
    @PostConstruct
    public void connectToPredefinedEndpoints() {
        if (!thirdPartyMcpConfig.isEnabled()) {
            logger.info("Third-party MCP functionality is disabled");
            return;
        }
        
        logger.info("Connecting to predefined third-party MCP endpoints from database");
        
        List<SysMcpEndpoint> endpoints = sysMcpEndpointService.getEnabledEndpoints();
        for (SysMcpEndpoint endpoint : endpoints) {
            try {
                Map<String, String> headers = new HashMap<>();
                
                // Add auth token if present
                if (endpoint.getAuthToken() != null && !endpoint.getAuthToken().isEmpty()) {
                    headers.put("Authorization", "Bearer " + endpoint.getAuthToken());
                }
                
                // Add other headers if present
                if (endpoint.getHeaders() != null && !endpoint.getHeaders().isEmpty()) {
                    try {
                        Map<String, String> additionalHeaders = JsonUtil.fromJson(
                            endpoint.getHeaders(), 
                            new com.fasterxml.jackson.core.type.TypeReference<Map<String, String>>() {}
                        );
                        headers.putAll(additionalHeaders);
                    } catch (Exception e) {
                        logger.warn("Failed to parse headers for endpoint {}: {}", endpoint.getUrl(), e.getMessage());
                    }
                }
                
                boolean connected = thirdPartyMcpManager.connectToEndpoint(
                    endpoint.getUrl(), 
                    headers
                );
                
                if (connected) {
                    logger.info("Successfully connected to third-party MCP endpoint: {}", endpoint.getUrl());
                } else {
                    logger.warn("Failed to connect to third-party MCP endpoint: {}", endpoint.getUrl());
                }
            } catch (Exception e) {
                logger.error("Error connecting to third-party MCP endpoint: {}", endpoint.getUrl(), e);
            }
        }
    }
}