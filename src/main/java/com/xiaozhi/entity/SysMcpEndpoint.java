package com.xiaozhi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * MCP endpoint entity
 */
@Data
@TableName("sys_mcp_endpoint")
@EqualsAndHashCode(callSuper = true)
public class SysMcpEndpoint extends BaseEntity {
    /**
     * Endpoint URL
     */
    private String url;
    
    /**
     * Endpoint name
     */
    private String name;
    
    /**
     * Authentication token (if required)
     */
    private String authToken;

    /**
     * 是否启用
     */
    private Boolean isEnabled;
}