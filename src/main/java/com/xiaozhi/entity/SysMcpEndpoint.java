package com.xiaozhi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * MCP endpoint entity
 */
@Data
@TableName("sys_mcp_endpoint")
public class SysMcpEndpoint implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * Endpoint URL
     */
    private String url;
    
    /**
     * Endpoint name
     */
    private String name;
    
    /**
     * Authentication token (if required)
     */
    private String authToken;
    
    /**
     * Other headers as JSON string
     */
    private String headers;
    
    /**
     * 是否启用
     */
    private Boolean enabled = true;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private Integer delFlag = 0;
}