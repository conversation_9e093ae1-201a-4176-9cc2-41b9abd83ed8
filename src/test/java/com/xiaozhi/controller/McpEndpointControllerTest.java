package com.xiaozhi.controller;

import com.xiaozhi.common.web.AjaxResult;
import com.xiaozhi.dialogue.llm.tool.mcp.thirdparty.ThirdPartyMcpService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for McpEndpointController
 */
class McpEndpointControllerTest {
    
    @Mock
    private ThirdPartyMcpService thirdPartyMcpService;
    
    @InjectMocks
    private McpEndpointController mcpEndpointController;
    
    private MockMvc mockMvc;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(mcpEndpointController).build();
    }
    
    @Test
    void testGetAvailableToolsWithEmptyUrl() {
        // Test with empty URL
        AjaxResult result = mcpEndpointController.getAvailableTools("", null);
        
        assertNotNull(result);
        assertEquals(500, result.getCode()); // Error code
        assertTrue(result.getMessage().contains("MCP URL不能为空"));
    }
    
    @Test
    void testGetAvailableToolsWithNullUrl() {
        // Test with null URL
        AjaxResult result = mcpEndpointController.getAvailableTools(null, null);
        
        assertNotNull(result);
        assertEquals(500, result.getCode()); // Error code
        assertTrue(result.getMessage().contains("MCP URL不能为空"));
    }
    
    @Test
    void testGetAvailableToolsWithInvalidUrl() {
        // Test with invalid URL that will cause connection failure
        String invalidUrl = "http://invalid-mcp-endpoint.local:12345";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer test-token");
        
        AjaxResult result = mcpEndpointController.getAvailableTools(invalidUrl, headers);
        
        assertNotNull(result);
        assertEquals(500, result.getCode()); // Error code
        assertTrue(result.getMessage().contains("无法连接到MCP端点") || 
                  result.getMessage().contains("获取MCP端点工具时发生错误"));
    }
    
    @Test
    void testControllerInitialization() {
        // Test that the controller is properly initialized
        assertNotNull(mcpEndpointController);
    }
}
