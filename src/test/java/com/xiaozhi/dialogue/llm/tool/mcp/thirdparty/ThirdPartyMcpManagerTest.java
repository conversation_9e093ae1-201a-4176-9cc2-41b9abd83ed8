package com.xiaozhi.dialogue.llm.tool.mcp.thirdparty;

import com.xiaozhi.config.McpConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = {ThirdPartyMcpManager.class, ThirdPartyMcpService.class, McpConfig.class})
@TestPropertySource(properties = {
    "xiaozhi.mcp.connection.timeout-ms=5000",
    "xiaozhi.mcp.connection.retry-count=1",
    "xiaozhi.mcp.connection.retry-interval-ms=100"
})
class ThirdPartyMcpManagerTest {
    
    @Autowired
    private ThirdPartyMcpManager thirdPartyMcpManager;
    
    @Test
    void testManagerInitialization() {
        assertNotNull(thirdPartyMcpManager);
    }
    
    @Test
    void testConnectAndDisconnect() {
        // Test with a dummy endpoint - this will fail but we can test the flow
        boolean connected = thirdPartyMcpManager.connectToEndpoint(
            "http://localhost:12345/invalid-endpoint", 
            Map.of("Authorization", "Bearer test-token")
        );
        
        // Should fail to connect to invalid endpoint
        assertFalse(connected);
        
        // Should not be connected
        assertFalse(thirdPartyMcpManager.isConnectedToEndpoint("http://localhost:12345/invalid-endpoint"));
        
        // Should return empty list for tools
        assertTrue(thirdPartyMcpManager.getToolsFromEndpoint("http://localhost:12345/invalid-endpoint").isEmpty());
        
        // Disconnect should handle gracefully even for non-connected endpoint
        // We're not asserting the return value as it may vary depending on implementation details
        thirdPartyMcpManager.disconnectFromEndpoint("http://localhost:12345/invalid-endpoint");
    }
}